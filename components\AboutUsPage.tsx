
import React, { useEffect, useRef, useState } from 'react';
import { ElisaFlourishIcon, ArrowRightIcon, HeartIcon, LaundryBubblesIcon, FreshSparkleIcon, ShoppingCartIcon, UserIcon } from './common/Icon';

const AboutUsPage: React.FC = () => {
  const missionImageUrl = "/assets/about2.png";
  const heroImageUrl = "/assets/about1.png";

  // Animation state management
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);
  const [animationComplete, setAnimationComplete] = useState(false);

  const values = [
    {
      icon: <LaundryBubblesIcon size={32} />,
      title: 'Uncompromising Quality & Efficacy',
      description: 'Our unique, market-tested formulas deliver a superior clean you can trust, every time. Experience the Le Prestine difference in every drop.',
    },
    {
      icon: <HeartIcon size={32} />,
      title: 'Safety & Your Well-being',
      description: 'Your peace of mind is paramount. Our products are meticulously designed to be safe for your family and home, free from harsh chemicals.',
    },
    {
      icon: <FreshSparkleIcon size={32} />,
      title: 'Pioneering a Greener Clean',
      description: "We're committed to a sustainable future. Our upcoming eco-friendly line will feature biodegradable ingredients and recyclable packaging.",
    },
  ];

  const solutions = [
    {
      icon: <ShoppingCartIcon size={28} />,
      title: 'Quality Meets Affordability',
      description: 'No more choosing. Get premium cleaning performance without the premium price tag. We bring top-tier products to every home.',
    },
    {
      icon: <FreshSparkleIcon size={28} />,
      title: 'All-in-One Convenience',
      description: 'Your complete laundry care and home hygiene essentials, all under one trusted name. Simplify your shopping, amplify your clean.',
    },
    {
      icon: <UserIcon size={28} />,
      title: 'Clarity and Trust',
      description: 'We believe in transparency. Our products are easy to understand and use, building confidence with every clean and catering to our diverse community.',
    },
    {
      icon: <HeartIcon size={28} />,
      title: 'Safety-First Formulations',
      description: 'Our innovative, non-harmful formulas mean a healthier home for you and your family. Effective cleaning doesn_t have to come with risks.',
    }
  ];

  // Intersection Observer for scroll-triggered animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);

          // Sophisticated staggered animation phases
          setTimeout(() => setAnimationPhase(1), 200);   // Hero section
          setTimeout(() => setAnimationPhase(2), 600);   // Mission section
          setTimeout(() => setAnimationPhase(3), 1000);  // Values grid
          setTimeout(() => setAnimationPhase(4), 1400);  // Solutions grid
          setTimeout(() => setAnimationPhase(5), 1800);  // Future section

          // Mark animation as complete
          setTimeout(() => setAnimationComplete(true), 2500);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [hasAnimated]);

  return (
    <div ref={sectionRef} className="min-h-screen bg-white font-sans relative overflow-hidden">
      {/* Enhanced Background with Animated Patterns */}
      <div className="absolute inset-0">
        {/* Floating Background Elements */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-brand-primary-lavender/15 to-transparent rounded-full blur-3xl animate-parallax-float"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-tl from-brand-primary-lavender-darker/10 to-transparent rounded-full blur-3xl animate-parallax-float animation-delay-300"></div>
        <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-gradient-to-r from-brand-primary-lavender/8 to-brand-primary-lavender-darker/8 rounded-full blur-2xl animate-pulse-slow"></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(222, 191, 242, 0.4) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20 lg:py-24">

        {/* Hero Section - Modern Bento Grid Layout */}
        <div className="max-w-7xl mx-auto">

          {/* Hero Title with Lavender Background */}
          <div className={`text-center mb-16 md:mb-20 transform-gpu bg-brand-primary-lavender rounded-3xl p-8 md:p-12 lg:p-16 shadow-2xl relative overflow-hidden ${
            animationPhase >= 1
              ? 'opacity-100 translate-y-0 scale-100'
              : 'opacity-0 translate-y-8 scale-95'
          }`}
          style={{
            transition: 'all 800ms cubic-bezier(0.34, 1.56, 0.64, 1) 200ms'
          }}>
            {/* Glass Morphism Overlay */}
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>

            {/* Floating Decorative Elements */}
            <div className="absolute top-8 right-8 w-20 h-20 bg-white/10 rounded-full blur-xl animate-parallax-float opacity-60"></div>
            <div className="absolute bottom-8 left-8 w-16 h-16 bg-white/15 rounded-full blur-lg animate-parallax-float animation-delay-400 opacity-50"></div>

            <div className="relative z-10">
              <h1 className="font-serif text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold mb-6 text-white">
                Discover Le Prestine
              </h1>
              <p className="text-xl sm:text-2xl lg:text-3xl text-white/90 max-w-4xl mx-auto leading-relaxed">
                Elevating everyday cleaning into an experience of purity, trust, and care.
              </p>
            </div>
          </div>

          {/* Modern Bento Grid Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-8 mb-20">

            {/* Mission Card - Large */}
            <div className={`lg:col-span-8 bg-white/80 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-white/50 shadow-2xl relative overflow-hidden group transform-gpu ${
              animationPhase >= 2
                ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                : 'opacity-0 translate-y-12 scale-95 rotate-1'
            } ${
              animationComplete
                ? 'hover:shadow-3xl hover:scale-[1.02] transition-all duration-500 ease-out'
                : ''
            }`}
            style={{
              transition: !animationComplete
                ? 'all 900ms cubic-bezier(0.34, 1.56, 0.64, 1) 600ms'
                : undefined
            }}>
              {/* Glass Morphism Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-brand-primary-lavender/8 via-transparent to-brand-primary-lavender-darker/5 opacity-60 group-hover:opacity-40 transition-opacity duration-500"></div>

              {/* Floating Decorative Elements */}
              <div className="absolute top-8 right-8 w-20 h-20 bg-brand-primary-lavender/15 rounded-full blur-xl animate-parallax-float opacity-60"></div>
              <div className="absolute bottom-8 left-8 w-16 h-16 bg-brand-primary-lavender-darker/10 rounded-full blur-lg animate-parallax-float animation-delay-400 opacity-50"></div>

              <div className="relative z-10">
                <span className="text-sm font-semibold uppercase tracking-wider text-brand-primary-lavender-darker mb-4 block">Our Purpose</span>
                <h2 className="font-serif text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                  Your Pristine Home, Our Passion
                </h2>
                <div className="space-y-4 text-gray-700 leading-relaxed">
                  <p className="text-base sm:text-lg">
                    At Le Prestine, you are at the heart of everything we do. Our mission goes beyond just selling products; we're dedicated to building a brand you can trust, synonymous with <strong className="text-brand-main-red font-semibold">quality, reliability, and your complete satisfaction.</strong>
                  </p>
                  <p className="text-base sm:text-lg">
                    We believe everyone deserves a sparkling clean home without compromise. That's why we offer high-quality, effective cleaning solutions at prices that make sense.
                  </p>
                  <p className="text-base sm:text-lg">
                    What truly sets us apart? Our <strong className="text-brand-primary-lavender-darker font-semibold">unique, in-house formulas.</strong> Developed with your safety and amazing results in mind, they're gentle yet powerful.
                  </p>
                </div>
              </div>
            </div>

            {/* Hero Image Card - Medium */}
            <div className={`lg:col-span-4 bg-white/60 backdrop-blur-xl rounded-3xl p-6 border border-white/50 shadow-2xl relative overflow-hidden group transform-gpu ${
              animationPhase >= 2
                ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                : 'opacity-0 translate-y-12 scale-95 rotate-2'
            } ${
              animationComplete
                ? 'hover:shadow-3xl hover:scale-105 transition-all duration-500 ease-out'
                : ''
            }`}
            style={{
              transition: !animationComplete
                ? 'all 900ms cubic-bezier(0.34, 1.56, 0.64, 1) 800ms'
                : undefined
            }}>
              {/* Glass Morphism Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-white/5 group-hover:from-white/30 group-hover:to-white/10 transition-all duration-500"></div>

              <div className="relative z-10 h-full flex flex-col">
                <div className="flex-1 rounded-2xl overflow-hidden mb-4">
                  <img
                    src={heroImageUrl}
                    alt="Linen sheets drying in a picturesque alleyway, evoking a sense of freshness and tradition."
                    className="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <div className="text-center">
                  <h3 className="font-serif text-lg font-semibold text-gray-900 mb-2">Tradition Meets Innovation</h3>
                  <p className="text-sm text-gray-600">Where time-honored cleaning wisdom meets modern science</p>
                </div>
              </div>
            </div>
          </div>
          {/* Values Section - Bento Grid */}
          <div className="mb-20">
            <div className={`text-center mb-12 md:mb-16 transform-gpu ${
              animationPhase >= 3
                ? 'opacity-100 translate-y-0 scale-100'
                : 'opacity-0 translate-y-8 scale-95'
            }`}
            style={{
              transition: 'all 800ms cubic-bezier(0.34, 1.56, 0.64, 1) 1000ms'
            }}>
              <span className="text-sm font-semibold uppercase tracking-wider text-brand-primary-lavender-darker mb-4 block">Our Guiding Principles</span>
              <h2 className="font-serif text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                The Le Prestine Promise
              </h2>
              <p className="text-gray-700 leading-relaxed max-w-2xl mx-auto text-base sm:text-lg">
                Built on values that matter to you and your family's well-being.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {values.map((value, index) => {
                const cardDelay = 1200 + (index * 200);
                return (
                  <div
                    key={value.title}
                    className={`bg-brand-primary-lavender rounded-3xl p-8 shadow-2xl relative overflow-hidden group transform-gpu ${
                      animationPhase >= 3
                        ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                        : 'opacity-0 translate-y-12 scale-90 rotate-2'
                    } ${
                      animationComplete
                        ? 'hover:shadow-3xl hover:scale-105 hover:bg-brand-primary-lavender-darker transition-all duration-500 ease-out'
                        : ''
                    }`}
                    style={{
                      transition: !animationComplete
                        ? `all 800ms cubic-bezier(0.34, 1.56, 0.64, 1) ${cardDelay}ms`
                        : undefined
                    }}
                  >
                    {/* Glass Morphism Background */}
                    <div className="absolute inset-0 bg-white/10 backdrop-blur-sm group-hover:bg-white/15 transition-all duration-500"></div>

                    {/* Floating Elements */}
                    <div className="absolute top-4 right-4 w-12 h-12 bg-white/10 rounded-full blur-lg animate-parallax-float opacity-50"></div>

                    <div className="relative z-10 text-center">
                      <div className="inline-flex p-4 bg-white/20 rounded-2xl mb-6 group-hover:scale-110 group-hover:bg-white/30 transition-all duration-300">
                        {React.cloneElement(value.icon as React.ReactElement<any>, {
                          className: "text-white",
                          size: 36
                        })}
                      </div>
                      <h3 className="font-serif text-xl sm:text-2xl font-semibold text-white mb-4 leading-tight">{value.title}</h3>
                      <p className="text-white/90 leading-relaxed text-sm sm:text-base">{value.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Solutions Section - Asymmetric Bento Grid */}
          <div className="mb-20">
            <div className={`text-center mb-12 md:mb-16 transform-gpu ${
              animationPhase >= 4
                ? 'opacity-100 translate-y-0 scale-100'
                : 'opacity-0 translate-y-8 scale-95'
            }`}
            style={{
              transition: 'all 800ms cubic-bezier(0.34, 1.56, 0.64, 1) 1400ms'
            }}>
              <span className="text-sm font-semibold uppercase tracking-wider text-brand-main-red mb-4 block">Listening to You</span>
              <h2 className="font-serif text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Solutions Inspired by Your Needs
              </h2>
              <p className="text-gray-700 leading-relaxed max-w-2xl mx-auto text-base sm:text-lg">
                We understand the challenges you face. That's why we're committed to providing solutions that make a real difference.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {solutions.map((solution, index) => {
                const cardDelay = 1600 + (index * 150);
                return (
                  <div
                    key={solution.title}
                    className={`bg-gradient-to-br from-brand-main-red to-brand-main-red-darker p-8 rounded-3xl shadow-2xl relative overflow-hidden group transform-gpu ${
                      animationPhase >= 4
                        ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                        : 'opacity-0 translate-y-12 scale-90 rotate-1'
                    } ${
                      animationComplete
                        ? 'hover:shadow-3xl hover:scale-105 hover:-translate-y-2 transition-all duration-500 ease-out'
                        : ''
                    }`}
                    style={{
                      transition: !animationComplete
                        ? `all 800ms cubic-bezier(0.34, 1.56, 0.64, 1) ${cardDelay}ms`
                        : undefined
                    }}
                  >
                    {/* Glass Morphism Overlay */}
                    <div className="absolute inset-0 bg-white/10 backdrop-blur-sm group-hover:bg-white/15 transition-all duration-500"></div>

                    {/* Floating Elements */}
                    <div className="absolute top-4 right-4 w-10 h-10 bg-white/10 rounded-full blur-md animate-parallax-float opacity-60"></div>

                    <div className="relative z-10 text-center text-white">
                      <div className="inline-flex p-4 bg-white/20 rounded-2xl mb-6 group-hover:scale-110 group-hover:bg-white/30 transition-all duration-300">
                        {React.cloneElement(solution.icon as React.ReactElement<any>, {
                          className: "text-white",
                          size: 32
                        })}
                      </div>
                      <h3 className="font-serif text-xl font-semibold mb-4 leading-tight">{solution.title}</h3>
                      <p className="text-white/90 leading-relaxed text-sm">{solution.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Future Vision Section - Final Bento Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-8">

            {/* Mission Image Card - Medium */}
            <div className={`lg:col-span-5 bg-white/60 backdrop-blur-xl rounded-3xl p-6 border border-white/50 shadow-2xl relative overflow-hidden group transform-gpu ${
              animationPhase >= 5
                ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                : 'opacity-0 translate-y-12 scale-95 rotate-2'
            } ${
              animationComplete
                ? 'hover:shadow-3xl hover:scale-105 transition-all duration-500 ease-out'
                : ''
            }`}
            style={{
              transition: !animationComplete
                ? 'all 900ms cubic-bezier(0.34, 1.56, 0.64, 1) 1800ms'
                : undefined
            }}>
              {/* Glass Morphism Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-white/5 group-hover:from-white/30 group-hover:to-white/10 transition-all duration-500"></div>

              <div className="relative z-10 h-full flex flex-col">
                <div className="flex-1 rounded-2xl overflow-hidden mb-4">
                  <img
                    src={missionImageUrl}
                    alt="Le Prestine cleaning products displayed in a bright, modern bathroom setting."
                    className="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <div className="text-center">
                  <h3 className="font-serif text-lg font-semibold text-gray-900 mb-2">Premium Quality Products</h3>
                  <p className="text-sm text-gray-600">Thoughtfully crafted for your home's complete care</p>
                </div>
              </div>
            </div>

            {/* Future Vision Card - Large */}
            <div className={`lg:col-span-7 bg-gradient-to-br from-brand-primary-lavender to-brand-primary-lavender-darker p-8 md:p-12 rounded-3xl shadow-2xl relative overflow-hidden group transform-gpu ${
              animationPhase >= 5
                ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                : 'opacity-0 translate-y-12 scale-95 rotate-1'
            } ${
              animationComplete
                ? 'hover:shadow-3xl hover:scale-[1.02] transition-all duration-500 ease-out'
                : ''
            }`}
            style={{
              transition: !animationComplete
                ? 'all 900ms cubic-bezier(0.34, 1.56, 0.64, 1) 2000ms'
                : undefined
            }}>
              {/* Glass Morphism Overlay */}
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm group-hover:bg-white/15 transition-all duration-500"></div>

              {/* Floating Decorative Elements */}
              <div className="absolute top-8 right-8 w-20 h-20 bg-white/10 rounded-full blur-xl animate-parallax-float opacity-60"></div>
              <div className="absolute bottom-8 left-8 w-16 h-16 bg-white/15 rounded-full blur-lg animate-parallax-float animation-delay-400 opacity-50"></div>

              <div className="relative z-10 text-white text-center">
                <ElisaFlourishIcon size={56} className="mx-auto mb-6 text-white opacity-80" />
                <h2 className="font-serif text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
                  The Future is Bright & Clean
                </h2>
                <div className="space-y-4 text-white/90 leading-relaxed mb-8">
                  <p className="text-base sm:text-lg">
                    Our vision is simple: to be your complete, trusted partner for a hygienic and vibrant home. We're constantly innovating and expanding, inspired by your needs.
                  </p>
                  <p className="text-base sm:text-lg">
                    The growing trust in Le Prestine fuels our passion to bring exceptional products to more homes like yours. With proven, safe formulas and unwavering commitment to quality.
                  </p>
                </div>
                <button
                  className="
                    bg-brand-main-red text-white font-semibold
                    py-4 px-8 rounded-2xl
                    shadow-xl hover:shadow-2xl hover:bg-brand-main-red-darker
                    transform hover:scale-105 active:scale-100
                    transition-all duration-300 ease-in-out
                    text-lg
                    focus:outline-none focus:ring-2 focus:ring-brand-main-red
                    focus:ring-offset-4 focus:ring-offset-brand-primary-lavender
                    inline-flex items-center space-x-3 group/btn
                  "
                  aria-label="Explore our product range"
                  // onClick={() => { /* TODO: Navigate to store page - onNavigate('Store') */ }}
                >
                  <span>Explore Our Products</span>
                  <ArrowRightIcon
                    size={20}
                    className="transition-transform duration-300 ease-in-out group-hover/btn:translate-x-1.5"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutUsPage;
