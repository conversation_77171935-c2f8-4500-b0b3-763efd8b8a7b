import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { LaundryBubblesIcon, HeartIcon, ToiletCleanerIcon, FreshSparkleIcon } from './common/Icon';

interface WhyChooseItem {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const WhyChooseSection: React.FC = () => {
  const navigate = useNavigate();
  const sectionRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);
  const [animationComplete, setAnimationComplete] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);

          // Trigger animation phases with sophisticated timing
          setTimeout(() => setAnimationPhase(1), 200);   // Header animation
          setTimeout(() => setAnimationPhase(2), 600);   // Cards start
          setTimeout(() => setAnimationPhase(3), 1800);  // CTA button

          // Mark animation as complete after all elements finish
          // Last card starts at 900ms + (3 * 200ms) = 1500ms, plus text delay 500ms + 200ms = 2200ms total
          setTimeout(() => setAnimationComplete(true), 2500);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  const whyChooseItems: WhyChooseItem[] = [
    {
      icon: <LaundryBubblesIcon size={40} />,
      title: 'Premium Quality Formulas',
      description: 'Our unique, market-tested formulas deliver superior cleaning power you can trust. Experience the Le Prestine difference in every drop with proven effectiveness.',
    },
    {
      icon: <HeartIcon size={40} />,
      title: 'Family Safety First',
      description: 'Your peace of mind is our priority. Our products are meticulously designed to be safe for your family and home, free from harsh chemicals.',
    },
    {
      icon: <ToiletCleanerIcon size={40} />,
      title: 'Trusted Results',
      description: 'Consistent, reliable performance that delivers spotless results every time. Join thousands of satisfied customers who trust Le Prestine for their cleaning needs.',
    },
    {
      icon: <FreshSparkleIcon size={40} />,
      title: 'Fresh Innovation',
      description: 'We continuously innovate to bring you the latest in cleaning technology, ensuring your home stays fresh, clean, and beautifully maintained.',
    },
  ];

  return (
    <section
      ref={sectionRef}
      className="py-16 md:py-24 bg-gradient-to-br from-brand-primary-lavender via-brand-primary-lavender to-brand-primary-lavender-darker relative overflow-hidden"
      aria-labelledby="why-choose-section-title"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-1"></div>
        <div className="absolute inset-0 bg-gradient-to-l from-transparent via-white/3 to-transparent transform skew-y-1"></div>
      </div>

      {/* Floating Decorative Elements */}
      <div className="absolute top-20 left-10 w-24 h-24 bg-brand-accent-teal/10 rounded-full blur-xl animate-parallax-float opacity-60"></div>
      <div className="absolute bottom-20 right-10 w-20 h-20 bg-white/10 rounded-full blur-lg animate-parallax-float animation-delay-500 opacity-50"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-brand-accent-teal/5 rounded-full blur-md animate-parallax-float animation-delay-300 opacity-40"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header with Magnetic Reveal Animation */}
        <div className="text-center mb-12 md:mb-16 relative">
          {/* Animated Badge */}
          <div className={`inline-block transition-all duration-1000 ease-out ${
            animationPhase >= 1
              ? 'opacity-100 translate-y-0 scale-100 rotate-0'
              : 'opacity-0 translate-y-8 scale-75 rotate-12'
          }`} style={{ transitionDelay: '100ms' }}>
            <span className="relative px-6 py-3 bg-gradient-to-r from-brand-main-red/20 to-white/20 backdrop-blur-sm rounded-full text-sm font-semibold tracking-widest uppercase text-white/90 mb-6 border border-white/20 inline-block overflow-hidden">
              {/* Shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full animate-shimmer"></div>
              Why Choose Le Prestine
            </span>
          </div>

          {/* Main Title with Split Animation */}
          <h2 id="why-choose-section-title" className="font-serif text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
            <div className={`transition-all duration-1200 ease-out ${
              animationPhase >= 1
                ? 'opacity-100 translate-x-0 scale-100'
                : 'opacity-0 -translate-x-12 scale-90'
            }`} style={{
              transitionDelay: '300ms',
              textShadow: '0 4px 20px rgba(0,0,0,0.3)'
            }}>
              <span className="text-white">The Le Prestine</span>
            </div>
            <div className={`block transition-all duration-1200 ease-out ${
              animationPhase >= 1
                ? 'opacity-100 translate-x-0 scale-100'
                : 'opacity-0 translate-x-12 scale-90'
            }`} style={{
              transitionDelay: '500ms',
              textShadow: '0 4px 20px rgba(9, 184, 166, 0.4)'
            }}>
              <span className="text-brand-main-red">Difference</span>
            </div>
          </h2>

          {/* Description with Typewriter Effect */}
          <div className={`transition-all duration-1000 ease-out ${
            animationPhase >= 1
              ? 'opacity-100 translate-y-0 scale-100'
              : 'opacity-0 translate-y-6 scale-95'
          }`} style={{ transitionDelay: '700ms' }}>
            <p className="text-white/90 text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed">
              Discover what makes Le Prestine the trusted choice for thousands of families seeking premium cleaning solutions that deliver exceptional results.
            </p>
          </div>
        </div>

        {/* Why Choose Items Grid with Magnetic Morphing */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-10">
          {whyChooseItems.map((item, index) => {
            const cardDelay = 900 + (index * 200);
            const iconDelay = cardDelay + 300;
            const textDelay = cardDelay + 500;

            return (
              <div
                key={item.title}
                className={`group relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 transform-gpu ${
                  // Entrance animation classes
                  animationPhase >= 2
                    ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                    : 'opacity-0 translate-y-20 scale-75 rotate-6'
                } ${
                  // Hover effects only after animation is complete
                  animationComplete
                    ? 'hover:bg-white/15 hover:scale-105 hover:shadow-2xl transition-all duration-300 ease-out'
                    : ''
                }`}
                style={{
                  // Entrance animation timing
                  transition: !animationComplete
                    ? `all 800ms cubic-bezier(0.34, 1.56, 0.64, 1) ${cardDelay}ms`
                    : undefined,
                  transformOrigin: 'bottom center'
                }}
              >
                {/* Glass morphism overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-brand-main-red/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                {/* Icon Container with Magnetic Morph */}
                <div className="relative mb-6">
                  <div className={`w-20 h-20 bg-gradient-to-br from-brand-main-red to-brand-main-red-darker rounded-2xl flex items-center justify-center text-white shadow-xl transform-gpu ${
                    // Entrance animation classes
                    animationPhase >= 2
                      ? 'opacity-100 scale-100 rotate-0'
                      : 'opacity-0 scale-0 rotate-180'
                  } ${
                    // Hover effects only after animation is complete
                    animationComplete
                      ? 'group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 ease-out'
                      : ''
                  }`}
                  style={{
                    // Entrance animation timing
                    transition: !animationComplete
                      ? `all 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55) ${iconDelay}ms`
                      : undefined,
                    transformOrigin: 'center'
                  }}>
                    <div className={`transition-all duration-400 ease-out ${
                      animationPhase >= 2
                        ? 'opacity-100 scale-100 rotate-0'
                        : 'opacity-0 scale-50 rotate-90'
                    }`} style={{ transitionDelay: `${iconDelay + 200}ms` }}>
                      {item.icon}
                    </div>
                  </div>

                  {/* Enhanced Icon glow effect */}
                  <div className={`absolute inset-0 w-20 h-20 bg-brand-main-red/30 rounded-2xl blur-lg transition-all duration-500 ${
                    animationPhase >= 2
                      ? 'opacity-60 scale-100'
                      : 'opacity-0 scale-0'
                  } ${
                    animationComplete
                      ? 'group-hover:opacity-100 group-hover:scale-110'
                      : ''
                  }`}
                  style={{
                    transitionDelay: !animationComplete ? `${iconDelay + 100}ms` : '0ms'
                  }}></div>

                  {/* Magnetic pulse rings */}
                  <div className={`absolute inset-0 w-20 h-20 rounded-2xl border-2 border-brand-main-red/20 transition-all duration-1000 ${
                    animationPhase >= 2
                      ? 'opacity-100 scale-100'
                      : 'opacity-0 scale-150'
                  }`} style={{ transitionDelay: `${iconDelay + 300}ms` }}></div>
                  <div className={`absolute inset-0 w-20 h-20 rounded-2xl border border-brand-main-red/10 transition-all duration-1200 ${
                    animationPhase >= 2
                      ? 'opacity-100 scale-110'
                      : 'opacity-0 scale-200'
                  }`} style={{ transitionDelay: `${iconDelay + 400}ms` }}></div>
                </div>

                {/* Content with Staggered Text Animation */}
                <div className="relative">
                  <h3 className={`font-serif text-xl sm:text-2xl font-bold mb-4 leading-tight transform-gpu ${
                    // Entrance animation classes
                    animationPhase >= 2
                      ? 'opacity-100 translate-y-0 text-white'
                      : 'opacity-0 translate-y-4 text-transparent'
                  } ${
                    // Hover effects only after animation is complete
                    animationComplete
                      ? 'group-hover:text-brand-main-red transition-all duration-300 ease-out'
                      : ''
                  }`}
                  style={{
                    // Entrance animation timing
                    transition: !animationComplete
                      ? `all 500ms ease-out ${textDelay}ms`
                      : undefined,
                    textShadow: animationPhase >= 2 ? '0 2px 10px rgba(0,0,0,0.3)' : 'none'
                  }}>
                    {item.title}
                  </h3>
                  <p className={`leading-relaxed text-sm sm:text-base transform-gpu ${
                    // Entrance animation classes
                    animationPhase >= 2
                      ? 'opacity-100 translate-y-0 text-white/80'
                      : 'opacity-0 translate-y-3 text-transparent'
                  } ${
                    // Hover effects only after animation is complete
                    animationComplete
                      ? 'group-hover:text-white/90 transition-all duration-300 ease-out'
                      : ''
                  }`}
                  style={{
                    // Entrance animation timing
                    transition: !animationComplete
                      ? `all 600ms ease-out ${textDelay + 200}ms`
                      : undefined
                  }}>
                    {item.description}
                  </p>
                </div>

                {/* Subtle border glow on hover */}
                <div className="absolute inset-0 rounded-2xl border border-brand-accent-teal/0 group-hover:border-brand-accent-teal/30 transition-all duration-500"></div>
              </div>
            );
          })}
        </div>

        {/* Bottom Call-to-Action with Magnetic Entrance */}
        <div className="text-center mt-12 md:mt-16">
          <div className={`transition-all duration-800 ease-out ${
            animationPhase >= 3
              ? 'opacity-100 translate-y-0 scale-100'
              : 'opacity-0 translate-y-8 scale-90'
          }`} style={{ transitionDelay: '100ms' }}>
            <p className="text-white/80 text-lg mb-6">
              Ready to experience the Le Prestine difference?
            </p>
          </div>

          <div className={`transition-all duration-1000 ease-out ${
            animationPhase >= 3
              ? 'opacity-100 translate-y-0 scale-100 rotate-0'
              : 'opacity-0 translate-y-12 scale-75 rotate-6'
          }`} style={{ transitionDelay: '300ms' }}>
            <button
              onClick={() => navigate('/store')}
              className="group relative bg-brand-accent-teal text-white font-semibold py-4 px-10 rounded-xl shadow-lg hover:shadow-2xl hover:bg-brand-accent-teal-darker transform hover:scale-105 active:scale-100 transition-all duration-300 ease-in-out text-lg focus:outline-none focus:ring-2 focus:ring-brand-accent-teal focus:ring-offset-2 focus:ring-offset-brand-main-red overflow-hidden"
              aria-label="Navigate to store page"
            >
              {/* Animated background shimmer */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>

              {/* Button glow effect */}
              <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Magnetic pulse effect */}
              <div className={`absolute inset-0 rounded-xl border-2 border-white/20 transition-all duration-2000 ${
                animationPhase >= 3
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-150'
              }`} style={{ transitionDelay: '800ms' }}></div>

              <span className="relative z-10">Explore Our Products</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseSection;
