
import React, { useEffect, useRef, useState } from 'react';
import { ArrowRightIcon } from './common/Icon';

const Hero: React.FC = () => {
  const benefitTags = ['Effective Formulas', 'Sparkling Clean', 'Gentle Care', 'Fresh Fragrance'];
  const heroImageUrl = "/assets/hero-image.png";
  const sectionRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="w-full overflow-hidden relative parallax-container"
    >
      {/* Enhanced Background with Patterns and Glass Morphism */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200">
        {/* Animated Background Patterns */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-brand-accent-teal/20 to-transparent rounded-full blur-3xl animate-parallax-float"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-tl from-brand-main-red/15 to-transparent rounded-full blur-3xl animate-parallax-float animation-delay-300"></div>
          <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-gradient-to-r from-brand-accent-teal/10 to-brand-main-red/10 rounded-full blur-2xl animate-pulse-slow"></div>
        </div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(9, 184, 166, 0.3) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      <div className="relative flex flex-col lg:flex-row min-h-[80vh] sm:min-h-[650px] md:min-h-[750px] lg:min-h-[800px] xl:min-h-[850px] gap-4 md:gap-6">
        {/* Enhanced Left Content Box */}
        <div
          className={`lg:w-[52%] relative overflow-hidden text-white p-8 py-10 sm:p-12 md:p-14 lg:p-16 xl:p-20 flex flex-col justify-center rounded-t-3xl lg:rounded-t-none lg:rounded-l-none lg:rounded-tr-3xl ${isVisible ? 'animate-slide-in-left' : 'opacity-0'}`}
          style={{
            background: 'linear-gradient(135deg, rgba(222, 191, 242, 0.95) 0%, rgba(222, 191, 242, 0.9) 50%, rgba(209, 168, 232, 0.95) 100%)',
          }}
        >
          {/* Glass Morphism Overlay */}
          <div className="absolute inset-0 hero-glass-morphism opacity-20"></div>

          {/* Subtle Pattern Overlay */}
          <div className="absolute inset-0 opacity-10" style={{
            backgroundImage: `linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.1) 50%, transparent 60%)`,
            backgroundSize: '20px 20px'
          }}></div>

          {/* Floating Decorative Elements */}
          <div className="absolute top-8 right-8 w-20 h-20 bg-white/10 rounded-full blur-xl animate-parallax-float"></div>
          <div className="absolute bottom-12 left-8 w-16 h-16 bg-brand-main-red/20 rounded-full blur-lg animate-parallax-float animation-delay-500"></div>

          <div className="relative z-10">
            {/* Brand Label */}
            <div className={`mb-6 ${isVisible ? 'animate-scale-in animation-delay-100' : 'opacity-0'}`}>
              <span className="inline-block px-4 py-2 bg-white/15 backdrop-blur-sm rounded-full text-xs font-semibold tracking-widest uppercase text-white/90 border border-white/20">
                Premium Cleaning Solutions
              </span>
            </div>

            <h1 className={`font-serif text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-10 leading-tight ${isVisible ? 'animate-slide-in-left animation-delay-200' : 'opacity-0'}`}>
              <span className="text-brand-main-red">Le Prestine:</span><br />
              <span className="text-brand-main-red">
                Elevate Your Clean.
              </span>
            </h1>

            {/* Enhanced CTA Button */}
            <button
              className={`relative bg-gradient-to-r from-brand-main-red to-brand-main-red-darker text-white font-medium py-3 pl-4 pr-8 rounded-full w-max flex items-center group transition-all duration-500 ease-out text-base mb-10 md:mb-12 focus:outline-none focus:ring-4 focus:ring-white/30 hover:shadow-2xl hover:scale-105 overflow-hidden ${isVisible ? 'animate-scale-in animation-delay-300' : 'opacity-0'}`}
              aria-label="Explore Our Range"
            >
              {/* Shimmer Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 animate-shimmer"></div>

              {/* Glass Morphism Background */}
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full"></div>

              <span className="relative bg-white text-brand-main-red rounded-full p-2.5 w-12 h-12 flex items-center justify-center transform transition-all duration-500 ease-out group-hover:scale-110 group-hover:rotate-12 group-hover:bg-opacity-95 mr-4 shadow-lg">
                <ArrowRightIcon size={22} strokeWidth={2.5} />
              </span>
              <span className="relative text-lg font-semibold tracking-wide">Explore Our Range</span>
            </button>

            {/* Enhanced Description */}
            <p className={`text-brand-main-red mb-10 md:mb-12 text-sm sm:text-base leading-relaxed max-w-lg backdrop-blur-sm ${isVisible ? 'animate-slide-in-left animation-delay-400' : 'opacity-0'}`}>
              Experience the art of pristine cleaning with Le Prestine. Our meticulously crafted formulas deliver exceptional results, transforming everyday chores into moments of satisfaction. Discover a new standard of clean for your home.
            </p>

            {/* Enhanced Benefit Tags */}
            <div className={`flex flex-wrap gap-x-3 gap-y-3 ${isVisible ? 'animate-slide-in-left animation-delay-500' : 'opacity-0'}`} role="list">
              {benefitTags.map((tag, index) => (
                <span
                  key={tag}
                  className={`relative py-2.5 px-5 border border-white/30 rounded-full text-xs sm:text-sm text-white hover:bg-brand-main-red hover:border-brand-main-red transition-all duration-300 ease-out cursor-default group overflow-hidden backdrop-blur-sm hover:scale-105 hover:shadow-lg ${isVisible ? 'animate-scale-in' : 'opacity-0'}`}
                  style={{ animationDelay: `${600 + index * 100}ms` }}
                  role="listitem"
                >
                  {/* Hover Background Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-brand-main-red/20 to-brand-main-red/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                  <span className="relative z-10 font-medium">{tag}</span>
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Right Image Box */}
        <div
          className={`lg:w-[48%] w-full min-h-[400px] sm:min-h-[500px] lg:min-h-0 relative overflow-hidden rounded-none lg:rounded-b-none lg:rounded-r-none lg:rounded-tl-3xl group ${isVisible ? 'animate-slide-in-right animation-delay-200' : 'opacity-0'}`}
        >
          {/* Enhanced Background with Parallax Effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300">
            {/* Parallax Image */}
            <img
              src={heroImageUrl}
              alt="Eco-friendly green cleaning products and hands"
              className="absolute inset-0 w-full h-full object-cover transition-all duration-700 ease-out group-hover:scale-110 group-hover:rotate-1"
            />

            {/* Glass Morphism Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-brand-accent-teal/10 via-transparent to-brand-main-red/10 opacity-60 group-hover:opacity-40 transition-opacity duration-500"></div>

            {/* Sophisticated Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent group-hover:from-black/10 transition-all duration-500"></div>

            {/* Floating Elements for Depth */}
            <div className="absolute top-8 right-8 w-24 h-24 bg-white/10 backdrop-blur-sm rounded-full animate-parallax-float opacity-60"></div>
            <div className="absolute bottom-12 left-8 w-20 h-20 bg-brand-accent-teal/20 backdrop-blur-sm rounded-full animate-parallax-float animation-delay-400 opacity-50"></div>

            {/* Subtle Pattern Overlay */}
            <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-500" style={{
              backgroundImage: `radial-gradient(circle at 2px 2px, rgba(255, 255, 255, 0.4) 1px, transparent 0)`,
              backgroundSize: '30px 30px'
            }}></div>
          </div>

          {/* Interactive Hover Content */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 bg-black/20 backdrop-blur-sm">
            <div className="text-center text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                <ArrowRightIcon size={24} strokeWidth={2} />
              </div>
              <p className="text-sm font-medium">Discover Our Products</p>
            </div>
          </div>
        </div>
      </div>

      {/* Simple bottom transition - matching original design */}
      <div className="bg-brand-accent-teal w-full h-6 rounded-t-3xl relative z-10 -mt-6"></div>
    </section>
  );
};

export default Hero;
